import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:crypto/crypto.dart';
import 'package:convert/convert.dart';
import 'package:collection/collection.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BLE Mesh Controller',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: BLEMeshControllerPage(),
    );
  }
}

class BLEMeshControllerPage extends StatefulWidget {
  @override
  _BLEMeshControllerPageState createState() => _BLEMeshControllerPageState();
}

class _BLEMeshControllerPageState extends State<BLEMeshControllerPage> {
  List<BluetoothDevice> _availableDevices = [];
  List<BluetoothDevice> _connectedDevices = [];
  bool _isScanning = false;
  String _statusText = 'Ready to scan for mesh devices';
  BLEMeshController? _meshController;

  @override
  void initState() {
    super.initState();
    _meshController = BLEMeshController();
    _initBluetooth();
    _loadSavedDevices();
  }

  Future<void> _loadSavedDevices() async {
    await _meshController!.loadSavedDevices();
    setState(() {
      // Trigger UI update
    });
  }

  Future<void> _initBluetooth() async {
    // Request permissions
    await _requestPermissions();

    // Check if Bluetooth is available
    bool isAvailable = await FlutterBluePlus.isAvailable;
    if (!isAvailable) {
      setState(() {
        _statusText = 'Bluetooth not available';
      });
      return;
    }

    // Turn on Bluetooth if it's off
    if (await FlutterBluePlus.adapterState.first != BluetoothAdapterState.on) {
      await FlutterBluePlus.turnOn();
    }

    setState(() {
      _statusText = 'Bluetooth initialized';
    });
  }

  Future<void> _requestPermissions() async {
    await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.locationWhenInUse,
    ].request();
  }

  Future<void> _startScanning() async {
    if (_isScanning) return;

    setState(() {
      _isScanning = true;
      _statusText = 'Scanning for provisioned BLE Mesh devices...';
      _availableDevices.clear();
    });

    try {
      // Start scanning for BLE Mesh devices with Proxy Service (for OnOff Server example)
      await FlutterBluePlus.startScan(
        timeout: Duration(seconds: 10),
        withServices: [
          Guid("00001828-0000-1000-8000-00805f9b34fb"), // Mesh Proxy Service
        ],
      );

      // Listen to scan results
      FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          // Add all devices found with Mesh Proxy Service
          if (!_availableDevices
              .any((device) => device.id == result.device.id)) {
            setState(() {
              _availableDevices.add(result.device);
            });
          }
        }
      });

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((scanning) => !scanning).first;

      setState(() {
        _isScanning = false;
        _statusText =
            'Scan complete. Found ${_availableDevices.length} ESP32 devices';
      });
    } catch (e) {
      setState(() {
        _isScanning = false;
        _statusText = 'Error scanning: $e';
      });
    }
  }

  bool _isLikelyESP32Device(ScanResult result) {
    // Check device name
    String deviceName = result.device.name.toLowerCase();
    if (deviceName.contains('esp') ||
        deviceName.contains('mesh') ||
        deviceName.contains('ble_mesh') ||
        deviceName.contains('esp32')) {
      return true;
    }

    // Check advertised services
    for (Guid serviceUuid in result.advertisementData.serviceUuids) {
      String uuid = serviceUuid.toString().toLowerCase();
      // Check for mesh services or common ESP32 services
      if (uuid == "00001828-0000-1000-8000-00805f9b34fb" || // Mesh Proxy
          uuid == "00001827-0000-1000-8000-00805f9b34fb" || // Mesh Provisioning
          uuid == "6e400001-b5a3-f393-e0a9-e50e24dcca9e" || // Nordic UART
          uuid.startsWith("12345678")) {
        // Custom ESP32 services often start like this
        return true;
      }
    }

    // Check manufacturer data for Espressif
    if (result.advertisementData.manufacturerData.isNotEmpty) {
      // Espressif company ID is 0x02E5
      if (result.advertisementData.manufacturerData.containsKey(0x02E5)) {
        return true;
      }
    }

    // If device name is empty but has connectable services, might be ESP32
    if (deviceName.isEmpty &&
        result.advertisementData.serviceUuids.isNotEmpty) {
      return true;
    }

    return false;
  }

  Future<void> _connectToDevice(BluetoothDevice device) async {
    setState(() {
      _statusText =
          'Connecting to ${device.name.isEmpty ? 'Unknown Device' : device.name}...';
    });

    try {
      bool success = await _meshController!.connectToDevice(device);
      if (success) {
        // Save device for future quick access
        await _meshController!.saveDevice(device);

        _connectedDevices.add(device);
        _availableDevices.remove(device);
        setState(() {
          _statusText =
              'Successfully connected to ${device.name.isEmpty ? 'Unknown Device' : device.name}';
        });
      } else {
        setState(() {
          _statusText =
              'Failed to connect to ${device.name.isEmpty ? 'Unknown Device' : device.name}';
        });
      }
    } catch (e) {
      setState(() {
        _statusText = 'Error connecting: $e';
      });
    }
  }

  Future<void> _sendCommand(BluetoothDevice device, String command) async {
    setState(() {
      _statusText =
          'Sending command to ${device.name.isEmpty ? 'Unknown Device' : device.name}...';
    });

    try {
      bool success = await _meshController!.sendCommand(device, command);
      setState(() {
        _statusText = success
            ? 'Command sent successfully to ${device.name.isEmpty ? 'Unknown Device' : device.name}'
            : 'Failed to send command to ${device.name.isEmpty ? 'Unknown Device' : device.name}';
      });
    } catch (e) {
      setState(() {
        _statusText = 'Error sending command: $e';
      });
    }
  }

  Future<void> _sendCustomData(BluetoothDevice device, String data) async {
    setState(() {
      _statusText =
          'Sending custom data to ${device.name.isEmpty ? 'Unknown Device' : device.name}...';
    });

    try {
      bool success = await _meshController!.sendCustomData(device, data);
      setState(() {
        _statusText = success
            ? 'Custom data sent successfully to ${device.name.isEmpty ? 'Unknown Device' : device.name}'
            : 'Failed to send custom data to ${device.name.isEmpty ? 'Unknown Device' : device.name}';
      });
    } catch (e) {
      setState(() {
        _statusText = 'Error sending custom data: $e';
      });
    }
  }

  Future<void> _disconnectDevice(BluetoothDevice device) async {
    setState(() {
      _statusText =
          'Disconnecting from ${device.name.isEmpty ? 'Unknown Device' : device.name}...';
    });

    try {
      await _meshController!.disconnectDevice(device);
      setState(() {
        _connectedDevices.remove(device);
        _statusText =
            'Disconnected from ${device.name.isEmpty ? 'Unknown Device' : device.name}';
      });
    } catch (e) {
      setState(() {
        _statusText = 'Error disconnecting: $e';
      });
    }
  }

  void _showGPIOControlDialog(BluetoothDevice device) {
    int selectedPin = 2;
    bool pinState = false;
    int pwmValue = 0;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text('GPIO Control'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Text('Pin: '),
                      DropdownButton<int>(
                        value: selectedPin,
                        items: [
                          2,
                          4,
                          5,
                          12,
                          13,
                          14,
                          15,
                          16,
                          17,
                          18,
                          19,
                          21,
                          22,
                          23
                        ]
                            .map((pin) => DropdownMenuItem(
                                value: pin, child: Text('$pin')))
                            .toList(),
                        onChanged: (value) {
                          setDialogState(() {
                            selectedPin = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      Text('Digital State: '),
                      Switch(
                        value: pinState,
                        onChanged: (value) {
                          setDialogState(() {
                            pinState = value;
                          });
                        },
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      Text('PWM (0-255): '),
                      Expanded(
                        child: Slider(
                          value: pwmValue.toDouble(),
                          min: 0,
                          max: 255,
                          divisions: 255,
                          label: pwmValue.toString(),
                          onChanged: (value) {
                            setDialogState(() {
                              pwmValue = value.round();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _meshController!
                        .sendGPIOControl(device, selectedPin, pinState);
                    Navigator.of(context).pop();
                  },
                  child: Text('Set Digital'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _meshController!
                        .sendPWMControl(device, selectedPin, pwmValue);
                    Navigator.of(context).pop();
                  },
                  child: Text('Set PWM'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _reconnectToSavedDevice(SavedDeviceInfo savedDevice) async {
    setState(() {
      _statusText = 'Attempting to reconnect to ${savedDevice.name}...';
    });

    try {
      // Start scanning specifically for this device
      await FlutterBluePlus.startScan(
        timeout: Duration(seconds: 5),
        withServices: [Guid("00001828-0000-1000-8000-00805f9b34fb")],
      );

      // Listen for the specific device
      bool deviceFound = false;
      FlutterBluePlus.scanResults.listen((results) async {
        for (ScanResult result in results) {
          if (result.device.id.toString() == savedDevice.id && !deviceFound) {
            deviceFound = true;
            await FlutterBluePlus.stopScan();
            await _connectToDevice(result.device);
            break;
          }
        }
      });

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((scanning) => !scanning).first;

      if (!deviceFound) {
        setState(() {
          _statusText =
              'Could not find ${savedDevice.name}. Make sure it\'s powered on and nearby.';
        });
      }
    } catch (e) {
      setState(() {
        _statusText = 'Error reconnecting to ${savedDevice.name}: $e';
      });
    }
  }

  void _navigateToProvisioningPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            ProvisioningPage(meshController: _meshController!),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('BLE Mesh Controller'),
        backgroundColor: Colors.blue,
        actions: [
          IconButton(
            icon: Icon(Icons.bug_report),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text('Debug Logs'),
                  content: Container(
                    width: double.maxFinite,
                    child: ListView(
                      children: _meshController!
                          .getLogs()
                          .map((log) =>
                              Text(log, style: TextStyle(fontSize: 12)))
                          .toList(),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('Close'),
                    ),
                  ],
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(Icons.add_circle_outline),
            tooltip: 'Provision New Device',
            onPressed: _navigateToProvisioningPage,
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_statusText),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _isScanning ? null : _startScanning,
                  child: Text(
                      _isScanning ? 'Scanning...' : 'Scan for Mesh Devices'),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _availableDevices.clear();
                      _statusText = 'Device list cleared';
                    });
                  },
                  child: Text('Clear List'),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Saved Devices:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Container(
              height: 100,
              child: _meshController!.getSavedDevices().isEmpty
                  ? Center(child: Text('No saved devices'))
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _meshController!.getSavedDevices().length,
                      itemBuilder: (context, index) {
                        final savedDevice =
                            _meshController!.getSavedDevices()[index];
                        return Card(
                          margin: EdgeInsets.only(right: 8),
                          child: Container(
                            width: 150,
                            padding: EdgeInsets.all(8),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  savedDevice.name,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'Last seen: ${savedDevice.lastSeen.day}/${savedDevice.lastSeen.month}',
                                  style: TextStyle(fontSize: 12),
                                ),
                                SizedBox(height: 8),
                                ElevatedButton(
                                  onPressed: () {
                                    // Try to reconnect to saved device
                                    _reconnectToSavedDevice(savedDevice);
                                  },
                                  child: Text('Connect'),
                                  style: ElevatedButton.styleFrom(
                                    minimumSize: Size(80, 30),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
            SizedBox(height: 16),
            Text(
              'Available Mesh Devices:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 1,
              child: ListView.builder(
                itemCount: _availableDevices.length,
                itemBuilder: (context, index) {
                  final device = _availableDevices[index];
                  return Card(
                    child: ListTile(
                      title: Text(
                          device.name.isEmpty ? 'Unknown Device' : device.name),
                      subtitle: Text(device.id.toString()),
                      trailing: ElevatedButton(
                        onPressed: () => _connectToDevice(device),
                        child: Text('Connect'),
                      ),
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Connected Devices:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 1,
              child: ListView.builder(
                itemCount: _connectedDevices.length,
                itemBuilder: (context, index) {
                  final device = _connectedDevices[index];
                  return Card(
                    child: ExpansionTile(
                      title: Text(
                          device.name.isEmpty ? 'Unknown Device' : device.name),
                      subtitle: Text(device.id.toString()),
                      children: [
                        Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  ElevatedButton(
                                    onPressed: () => _sendCommand(device, 'ON'),
                                    child: Text('ON'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green),
                                  ),
                                  ElevatedButton(
                                    onPressed: () =>
                                        _sendCommand(device, 'OFF'),
                                    child: Text('OFF'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red),
                                  ),
                                  ElevatedButton(
                                    onPressed: () =>
                                        _sendCommand(device, 'STATUS'),
                                    child: Text('STATUS'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  ElevatedButton(
                                    onPressed: () => _meshController!
                                        .requestDeviceInfo(device),
                                    child: Text('INFO'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.purple),
                                  ),
                                  ElevatedButton(
                                    onPressed: () => _meshController!
                                        .sendSensorRequest(device, 'TEMP'),
                                    child: Text('TEMP'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.orange),
                                  ),
                                  ElevatedButton(
                                    onPressed: () =>
                                        _showGPIOControlDialog(device),
                                    child: Text('GPIO'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.teal),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      decoration: InputDecoration(
                                        labelText: 'Custom Data',
                                        border: OutlineInputBorder(),
                                      ),
                                      onSubmitted: (value) {
                                        if (value.isNotEmpty) {
                                          _sendCustomData(device, value);
                                        }
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: () => _disconnectDevice(device),
                                    child: Text('Disconnect'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.orange),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BLEMeshController {
  // Add this field to the BLEMeshController class
  int _nextUnicastAddress =
      0x0002; // Start from 0x0002, 0x0001 is for provisioner

  // Add this field to track the current provisioning state
  BluetoothCharacteristic? _currentDataInChar;
  bool _isProvisioning = false;

  // Your existing fields
  // BLE Mesh Service UUIDs
  static const String MESH_PROXY_SERVICE_UUID =
      "00001828-0000-1000-8000-00805f9b34fb";

  // BLE Mesh Characteristic UUIDs
  static const String MESH_PROXY_DATA_IN_UUID =
      "00002add-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROXY_DATA_OUT_UUID =
      "00002ade-0000-1000-8000-00805f9b34fb";

  // Add these constants to the BLEMeshController class
  static const String MESH_PROVISIONING_SERVICE_UUID =
      "00001827-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROVISIONING_DATA_IN_UUID =
      "00002adb-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROVISIONING_DATA_OUT_UUID =
      "00002adc-0000-1000-8000-00805f9b34fb";

  // Pre-configured network and application keys (using ESP32 BLE Mesh example defaults)
  final Uint8List _netKey = Uint8List.fromList([
    0x00,
    0x20,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12
  ]);
  final Uint8List _appKey = Uint8List.fromList([
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12,
    0x12
  ]);

  // Connected devices and their information
  final Map<String, MeshNodeInfo> _connectedNodes = {};

  // Sequence number for mesh messages
  int _sequenceNumber = 1;

  // Device status tracking
  final Map<String, DeviceStatus> _deviceStatuses = {};

  // Saved devices for quick reconnection
  final Map<String, SavedDeviceInfo> _savedDevices = {};

  final List<String> _debugLogs = [];

  void addLog(String log) {
    _debugLogs.add('[${DateTime.now().toIso8601String()}] $log');
    if (_debugLogs.length > 200) {
      _debugLogs.removeAt(0); // Limit log size
    }
  }

  List<String> getLogs() => List.unmodifiable(_debugLogs);

  Future<bool> connectToDevice(BluetoothDevice device) async {
    try {
      print(
          'Connecting to device: ${device.name.isEmpty ? device.id.toString() : device.name}');
      addLog(
          'Connecting to device: ${device.name.isEmpty ? device.id.toString() : device.name}');
      // Check if device is already connected
      if (await device.connectionState.first ==
          BluetoothConnectionState.connected) {
        print('Device already connected, discovering services...');
        addLog('Device already connected, discovering services...');
      } else {
        // Connect to device with timeout
        await device.connect(timeout: Duration(seconds: 15));
        print('BLE connection established');
        addLog('BLE connection established');
      }

      // Request a larger MTU size right after connecting
      await _requestMtu(device, 512);

      // Wait a moment for connection to stabilize
      await Future.delayed(Duration(milliseconds: 500));

      // Discover services
      print('Discovering services...');
      addLog('Discovering services...');
      List<BluetoothService> services = await device.discoverServices();
      print('Found ${services.length} services');
      addLog('Found ${services.length} services');

      // Print all available services for debugging
      for (BluetoothService service in services) {
        print('Service: ${service.uuid.toString()}');
        addLog('Service: ${service.uuid.toString()}');
        for (BluetoothCharacteristic char in service.characteristics) {
          print('  Characteristic: ${char.uuid.toString()}');
          addLog('  Characteristic: ${char.uuid.toString()}');
        }
      }

      //// Try to find mesh proxy service first
      // BluetoothService? targetService = services.firstWhereOrNull((service) =>
      //     service.uuid.toString().toLowerCase() ==
      //     MESH_PROXY_SERVICE_UUID.toLowerCase());

      BluetoothService? targetService = services.firstWhereOrNull((service) {
        String uuid = service.uuid.toString().toLowerCase();
        return uuid == "1828" || uuid == MESH_PROXY_SERVICE_UUID.toLowerCase();
      });

      if (targetService != null) {
        print('Found Mesh Proxy Service: ${targetService.uuid}');
        addLog('Found Mesh Proxy Service: ${targetService.uuid}');
      } else {
        print(
            'Mesh proxy service not found, looking for alternative services...');
        addLog(
            'Mesh proxy service not found, looking for alternative services...');

        // Look for common ESP32 services
        List<String> alternativeServiceUUIDs = [
          "0000180f-0000-1000-8000-00805f9b34fb", // Battery Service
          "0000180a-0000-1000-8000-00805f9b34fb", // Device Information Service
          "6e400001-b5a3-f393-e0a9-e50e24dcca9e", // Nordic UART Service
          "12345678-1234-1234-1234-123456789abc", // Custom service
        ];

        for (String uuid in alternativeServiceUUIDs) {
          targetService = services.firstWhereOrNull((service) =>
              service.uuid.toString().toLowerCase() == uuid.toLowerCase());
          if (targetService != null) {
            print('Found alternative service: ${targetService.uuid}');
            addLog('Found alternative service: ${targetService.uuid}');
            break;
          }
        }

        // If still no service found, use the first available service with characteristics
        if (targetService == null && services.isNotEmpty) {
          for (BluetoothService service in services) {
            if (service.characteristics.isNotEmpty) {
              targetService = service;
              print(
                  'Using first available service with characteristics: ${service.uuid}');
              addLog(
                  'Using first available service with characteristics: ${service.uuid}');
              break;
            }
          }
        } else {
          print('Found mesh proxy service: ${targetService!.uuid}');
          addLog('Found mesh proxy service: ${targetService!.uuid}');
        }
      }

      if (targetService == null) {
        print('No suitable service found');
        addLog('No suitable service found');
        await device.disconnect();
        return false;
      }

      // Get characteristics - try mesh proxy first, then fallback to any writable characteristics
      BluetoothCharacteristic? dataInChar;
      BluetoothCharacteristic? dataOutChar;

      //// Try to find mesh proxy characteristics
      // dataInChar = targetService.characteristics.firstWhereOrNull((char) =>
      //     char.uuid.toString().toLowerCase() ==
      //     MESH_PROXY_DATA_IN_UUID.toLowerCase());

      dataInChar = targetService.characteristics.firstWhereOrNull((char) {
        String uuid = char.uuid.toString().toLowerCase();
        bool isDataIn =
            uuid == "2add" || uuid == MESH_PROXY_DATA_IN_UUID.toLowerCase();
        if (isDataIn) {
          print(
              'Found data in characteristic: ${char.uuid}, properties: ${char.properties}');
          addLog(
              'Found data in characteristic: ${char.uuid}, properties: ${char.properties}');
        }
        return isDataIn;
      });

      // dataOutChar = targetService.characteristics.firstWhereOrNull((char) =>
      //     char.uuid.toString().toLowerCase() ==
      //     MESH_PROXY_DATA_OUT_UUID.toLowerCase());

      dataOutChar = targetService.characteristics.firstWhereOrNull((char) {
        String uuid = char.uuid.toString().toLowerCase();
        return uuid == "2ade" || uuid == MESH_PROXY_DATA_OUT_UUID.toLowerCase();
      });

      // If mesh proxy characteristics not found, use any available characteristics
      if (dataInChar == null || dataOutChar == null) {
        print(
            'Mesh proxy characteristics not found, using available characteristics...');
        addLog(
            'Mesh proxy characteristics not found, using available characteristics...');

        for (BluetoothCharacteristic char in targetService.characteristics) {
          print('Characteristic ${char.uuid}: properties = ${char.properties}');
          addLog(
              'Characteristic ${char.uuid}: properties = ${char.properties}');

          if (dataInChar == null && char.properties.write) {
            dataInChar = char;
            print('Using ${char.uuid} as data input characteristic');
            addLog('Using ${char.uuid} as data input characteristic');
          }

          if (dataOutChar == null &&
              (char.properties.notify || char.properties.indicate)) {
            dataOutChar = char;
            print('Using ${char.uuid} as data output characteristic');
            addLog('Using ${char.uuid} as data output characteristic');
          }
        }

        // If still no suitable characteristics, use first available ones
        if (dataInChar == null && targetService.characteristics.isNotEmpty) {
          dataInChar = targetService.characteristics.first;
          print(
              'Using first characteristic as fallback input: ${dataInChar.uuid}');
          addLog(
              'Using first characteristic as fallback input: ${dataInChar.uuid}');
        }

        if (dataOutChar == null && targetService.characteristics.length > 1) {
          dataOutChar = targetService.characteristics[1];
          print(
              'Using second characteristic as fallback output: ${dataOutChar.uuid}');
          addLog(
              'Using second characteristic as fallback output: ${dataOutChar.uuid}');
        } else if (dataOutChar == null) {
          dataOutChar = dataInChar; // Use same characteristic for both
          print('Using same characteristic for input/output');
          addLog('Using same characteristic for input/output');
        }
      }

      if (dataInChar == null) {
        print('No writable characteristic found');
        addLog('No writable characteristic found');
        await device.disconnect();
        return false;
      }

      // Enable notifications if possible
      if (dataOutChar != null &&
          (dataOutChar.properties.notify || dataOutChar.properties.indicate)) {
        try {
          await dataOutChar.setNotifyValue(true);
          print('Notifications enabled on ${dataOutChar.uuid}');
          addLog('Notifications enabled on ${dataOutChar.uuid}');
        } catch (e) {
          print('Failed to enable notifications: $e');
          addLog('Failed to enable notifications: $e');
        }
      }

      // Generate a unicast address for this device
      int unicastAddress = 0x0001 + _connectedNodes.length;

      // Store node information
      _connectedNodes[device.id.toString()] = MeshNodeInfo(
        device: device,
        unicastAddress: unicastAddress,
        netKey: _netKey,
        appKey: _appKey,
        dataInChar: dataInChar,
        dataOutChar: dataOutChar,
      );

      print(
          'Successfully connected to device with address: 0x${unicastAddress.toRadixString(16).padLeft(4, '0')}');
      addLog(
          'Successfully connected to device with address: 0x${unicastAddress.toRadixString(16).padLeft(4, '0')}');
      return true;
    } catch (e) {
      print('Error connecting to device: $e');
      addLog('Error connecting to device: $e');
      try {
        await device.disconnect();
      } catch (disconnectError) {
        print('Error during disconnect: $disconnectError');
        addLog('Error during disconnect: $disconnectError');
      }
      return false;
    }
  }

  Future<void> disconnectDevice(BluetoothDevice device) async {
    try {
      _connectedNodes.remove(device.id.toString());
      await device.disconnect();
      print('Disconnected from device: ${device.name}');
      addLog('Disconnected from device: ${device.name}');
    } catch (e) {
      print('Error disconnecting from device: $e');
      addLog('Error disconnecting from device: $e');
    }
  }

  Future<bool> sendCommand(BluetoothDevice device, String command) async {
    try {
      MeshNodeInfo? nodeInfo = _connectedNodes[device.id.toString()];
      if (nodeInfo == null) {
        print('Device not connected');
        addLog('Device not connected');
        return false;
      }

      // Verify characteristic availability
      if (nodeInfo.dataInChar == null) {
        print('Data characteristic not available');
        addLog('Data characteristic not available');
        return false;
      }

      //// Check if device is still connected
      Future<bool> isDeviceConnected(BluetoothDevice device) async {
        try {
          BluetoothConnectionState state = await device.connectionState.first;
          return state == BluetoothConnectionState.connected;
        } catch (e) {
          print('Error checking connection state: $e');
          addLog('Error checking connection state: $e');
          return false;
        }
      }

      if (!await isDeviceConnected(device)) {
        print('Device appears disconnected, verifying connection...');
        addLog('Device appears disconnected, verifying connection...');

        // Try to get services as a more reliable connection check
        try {
          await device.discoverServices();
          print('Device is actually connected, continuing...');
          addLog('Device is actually connected, continuing...');
        } catch (e) {
          // Now we're sure it's disconnected
          print('Device confirmed disconnected, attempting to reconnect...');
          addLog('Device confirmed disconnected, attempting to reconnect...');
          bool reconnected = await connectToDevice(device);
          if (!reconnected) {
            print('Failed to reconnect to device');
            addLog('Failed to reconnect to device');
            return false;
          }
          nodeInfo = _connectedNodes[device.id.toString()];
        }
      }

      // Create mesh message for the command
      Uint8List meshMessage = _createMeshMessage(nodeInfo!, command);

      // Verify message creation
      if (meshMessage.isEmpty) {
        print('Failed to create mesh message');
        addLog('Failed to create mesh message');
        return false;
      }

      print(
          'Sending message: ${meshMessage.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
      addLog(
          'Sending message: ${meshMessage.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');

      // Send message through data in characteristic with retry mechanism
      int retryCount = 0;
      while (retryCount < 3) {
        try {
          // Check if the characteristic actually supports write
          // await nodeInfo.dataInChar!.write(meshMessage, withoutResponse: false);
          if (!nodeInfo.dataInChar!.properties.write &&
              !nodeInfo.dataInChar!.properties.writeWithoutResponse) {
            print('Characteristic does not support write operations');
            addLog('Characteristic does not support write operations');
            return false;
          }

          // Use writeWithoutResponse if write is not supported
          bool useWithoutResponse = !nodeInfo.dataInChar!.properties.write &&
              nodeInfo.dataInChar!.properties.writeWithoutResponse;

          await nodeInfo.dataInChar!
              .write(meshMessage, withoutResponse: useWithoutResponse);
          print('Command sent successfully: $command');
          addLog('Command sent successfully: $command');
          return true;
        } catch (writeError) {
          print('Attempt ${retryCount + 1} failed: $writeError');
          addLog('Attempt ${retryCount + 1} failed: $writeError');
          retryCount++;
          if (retryCount < 3) {
            await Future.delayed(Duration(milliseconds: 200));
          }
        }
      }

      print('Failed to send command after 3 attempts');
      addLog('Failed to send command after 3 attempts');
      return false;
    } catch (e) {
      print('Error sending command: $e');
      addLog('Error sending command: $e');
      return false;
    }
  }

  Future<bool> sendCustomData(BluetoothDevice device, String data) async {
    try {
      MeshNodeInfo? nodeInfo = _connectedNodes[device.id.toString()];
      if (nodeInfo == null) {
        print('Device not connected');
        addLog('Device not connected');
        return false;
      }

      // Verify characteristic availability
      if (nodeInfo.dataInChar == null) {
        print('Data characteristic not available');
        addLog('Data characteristic not available');
        return false;
      }

      //// Check if device is still connected
      Future<bool> isDeviceConnected(BluetoothDevice device) async {
        try {
          BluetoothConnectionState state = await device.connectionState.first;
          return state == BluetoothConnectionState.connected;
        } catch (e) {
          print('Error checking connection state: $e');
          addLog('Error checking connection state: $e');
          return false;
        }
      }

      if (!await isDeviceConnected(device)) {
        print('Device appears disconnected, verifying connection...');
        addLog('Device appears disconnected, verifying connection...');

        // Try to get services as a more reliable connection check
        try {
          await device.discoverServices();
          print('Device is actually connected, continuing...');
          addLog('Device is actually connected, continuing...');
        } catch (e) {
          // Now we're sure it's disconnected
          print('Device confirmed disconnected, attempting to reconnect...');
          addLog('Device confirmed disconnected, attempting to reconnect...');
          bool reconnected = await connectToDevice(device);
          if (!reconnected) {
            print('Failed to reconnect to device');
            addLog('Failed to reconnect to device');
            return false;
          }
          nodeInfo = _connectedNodes[device.id.toString()];
          if (nodeInfo == null) {
            print('Device reconnected but node info not found');
            addLog('Device reconnected but node info not found');
            return false;
          }
        }
      }

      // Create custom mesh message
      Uint8List meshMessage = _createCustomMeshMessage(nodeInfo, data);

      print(
          'Sending message: ${meshMessage.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
      addLog(
          'Sending message: ${meshMessage.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');

      // Send message through data in characteristic with retry mechanism
      int retryCount = 0;
      while (retryCount < 3) {
        try {
          // Check if the characteristic actually supports write
          if (nodeInfo.dataInChar == null ||
              (!nodeInfo.dataInChar!.properties.write &&
                  !nodeInfo.dataInChar!.properties.writeWithoutResponse)) {
            print('Characteristic does not support write operations');
            addLog('Characteristic does not support write operations');
            return false;
          }

          // Use writeWithoutResponse if write is not supported
          bool useWithoutResponse = !nodeInfo.dataInChar!.properties.write &&
              nodeInfo.dataInChar!.properties.writeWithoutResponse;

          await nodeInfo.dataInChar!
              .write(meshMessage, withoutResponse: useWithoutResponse);
          print('Custom data sent successfully: $data');
          addLog('Custom data sent successfully: $data');
          return true;
        } catch (writeError) {
          print('Attempt ${retryCount + 1} failed: $writeError');
          addLog('Attempt ${retryCount + 1} failed: $writeError');
          retryCount++;
          if (retryCount < 3) {
            await Future.delayed(Duration(milliseconds: 200));
          }
        }
      }

      print('Failed to send custom data after 3 attempts');
      addLog('Failed to send custom data after 3 attempts');
      return false;
    } catch (e) {
      print('Error sending custom data: $e');
      addLog('Error sending custom data: $e');
      return false;
    }
  }

  // Enhanced methods for ESP32 specific controls
  Future<bool> sendGPIOControl(
      BluetoothDevice device, int pin, bool state) async {
    String command = 'GPIO:$pin:${state ? '1' : '0'}';
    return await sendCustomData(device, command);
  }

  Future<bool> sendPWMControl(
      BluetoothDevice device, int pin, int dutyCycle) async {
    String command = 'PWM:$pin:$dutyCycle';
    return await sendCustomData(device, command);
  }

  Future<bool> sendSensorRequest(
      BluetoothDevice device, String sensorType) async {
    String command = 'SENSOR:$sensorType';
    return await sendCustomData(device, command);
  }

  Future<bool> sendConfigUpdate(
      BluetoothDevice device, String config, String value) async {
    String command = 'CONFIG:$config:$value';
    return await sendCustomData(device, command);
  }

  Future<bool> requestDeviceInfo(BluetoothDevice device) async {
    return await sendCommand(device, 'INFO');
  }

  Future<bool> resetDevice(BluetoothDevice device) async {
    return await sendCommand(device, 'RESET');
  }

  // Device management methods
  Future<void> saveDevice(BluetoothDevice device) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    SavedDeviceInfo savedInfo = SavedDeviceInfo(
      id: device.id.toString(),
      name: device.name.isEmpty ? 'ESP32 Mesh Node' : device.name,
      lastSeen: DateTime.now(),
    );

    _savedDevices[device.id.toString()] = savedInfo;

    // Save to persistent storage
    List<String> savedDeviceIds = prefs.getStringList('saved_devices') ?? [];
    if (!savedDeviceIds.contains(device.id.toString())) {
      savedDeviceIds.add(device.id.toString());
      await prefs.setStringList('saved_devices', savedDeviceIds);
      await prefs.setString('device_${device.id}_name', savedInfo.name);
      await prefs.setString(
          'device_${device.id}_lastSeen', savedInfo.lastSeen.toIso8601String());
    }
  }

  Future<void> loadSavedDevices() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> savedDeviceIds = prefs.getStringList('saved_devices') ?? [];

    for (String deviceId in savedDeviceIds) {
      String? name = prefs.getString('device_${deviceId}_name');
      String? lastSeenStr = prefs.getString('device_${deviceId}_lastSeen');

      if (name != null && lastSeenStr != null) {
        _savedDevices[deviceId] = SavedDeviceInfo(
          id: deviceId,
          name: name,
          lastSeen: DateTime.parse(lastSeenStr),
        );
      }
    }
  }

  Future<void> removeSavedDevice(String deviceId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> savedDeviceIds = prefs.getStringList('saved_devices') ?? [];

    savedDeviceIds.remove(deviceId);
    await prefs.setStringList('saved_devices', savedDeviceIds);
    await prefs.remove('device_${deviceId}_name');
    await prefs.remove('device_${deviceId}_lastSeen');

    _savedDevices.remove(deviceId);
  }

  void updateDeviceStatus(String deviceId, DeviceStatus status) {
    _deviceStatuses[deviceId] = status;
  }

  List<int> _calculateMIC(List<int> message, Uint8List netKey) {
    // This is a placeholder using HMAC-SHA256 for demonstration.
    final hmac = Hmac(sha256, netKey);
    final digest = hmac.convert(message);
    // Return the first 4 bytes as MIC (real BLE Mesh uses AES-CMAC and different length)
    return digest.bytes.sublist(0, 4);
  }

  DeviceStatus? getDeviceStatus(String deviceId) {
    return _deviceStatuses[deviceId];
  }

  List<SavedDeviceInfo> getSavedDevices() {
    return _savedDevices.values.toList();
  }

  Uint8List _createMeshMessage(MeshNodeInfo nodeInfo, String command) {
    // Create proper BLE Mesh message for ESP32 OnOff Server example
    List<int> meshMessage = [];

    // Proxy PDU Type: Network PDU (0x00)
    meshMessage.add(0x00);

    // Network header
    meshMessage.add(0x68); // IVI(0) + NID(0x08)
    meshMessage.add(0x7F); // CTL(0) + TTL(0x7F)

    // Sequence Number (3 bytes)
    int seqNum = _sequenceNumber++;
    meshMessage.addAll([
      (seqNum >> 16) & 0xFF,
      (seqNum >> 8) & 0xFF,
      seqNum & 0xFF,
    ]);

    // Source Address (2 bytes) - Provisioner address (0x0001)
    meshMessage.addAll([0x00, 0x01]);

    // Destination Address (2 bytes) - Node's unicast address
    meshMessage.addAll([
      (nodeInfo.unicastAddress >> 8) & 0xFF,
      nodeInfo.unicastAddress & 0xFF,
    ]);

    // Message opcode and parameters
    if (command == 'ON') {
      meshMessage
          .addAll([0x82, 0x02, 0x01]); // Generic OnOff Set with value 0x01
    } else if (command == 'OFF') {
      meshMessage
          .addAll([0x82, 0x02, 0x00]); // Generic OnOff Set with value 0x00
    } else if (command == 'STATUS') {
      meshMessage.add(0x82); // Generic OnOff Get
    } else if (command == 'INFO') {
      meshMessage.add(0x80); // Device Composition Get
    }

    // Calculate and add MIC (4 bytes)
    List<int> mic = _calculateMIC(meshMessage, nodeInfo.netKey);
    meshMessage.addAll(mic);

    return Uint8List.fromList(meshMessage);
  }

  Uint8List _createCustomMeshMessage(MeshNodeInfo nodeInfo, String data) {
    // Create vendor-specific mesh message for custom data
    List<int> meshMessage = [];

    // Proxy PDU Type: Network PDU
    meshMessage.add(0x00);

    // Network Header
    meshMessage.add(0x68); // IVI(0) + NID(0x08)
    meshMessage.add(0x7F); // CTL(0) + TTL(0x7F)

    // Sequence Number (3 bytes)
    int seqNum = _sequenceNumber++;
    meshMessage.addAll([
      (seqNum >> 16) & 0xFF,
      (seqNum >> 8) & 0xFF,
      seqNum & 0xFF,
    ]);

    // Source Address (2 bytes)
    meshMessage.addAll([0x00, 0x01]);

    // Destination Address (2 bytes)
    meshMessage.addAll([
      (nodeInfo.unicastAddress >> 8) & 0xFF,
      nodeInfo.unicastAddress & 0xFF,
    ]);

    // Vendor Model Message (3-byte opcode for vendor messages)
    meshMessage.addAll([0xC0, 0x00, 0x01]); // Vendor opcode

    // Custom data payload (limit for BLE MTU)
    List<int> dataBytes = utf8.encode(data);
    meshMessage.addAll(dataBytes.take(10)); // Limit to 10 bytes

    return Uint8List.fromList(meshMessage);
  }

  // Add this method to the BLEMeshController class
  Future<bool> provisionDevice(
    BluetoothDevice device, {
    Uint8List? netKey,
    Uint8List? appKey,
    int? unicastAddress,
    Duration timeout = const Duration(seconds: 120),
  }) async {
    try {
      _isProvisioning = true;
      print('Starting provisioning for device: ${device.name}');
      addLog('Starting provisioning for device: ${device.name}');

      // Connect to the device if not already connected
      if (await device.connectionState.first !=
          BluetoothConnectionState.connected) {
        await device.connect(timeout: Duration(seconds: 30));
      }
      print('Connected to device for provisioning');
      addLog('Connected to device for provisioning');

      // Request MTU
      await _requestMtu(device, 512);

      // Discover services
      List<BluetoothService> services = await device.discoverServices();
      print('Discovered ${services.length} services');
      addLog('Discovered ${services.length} services');

      // Find the provisioning service
      BluetoothService? provisioningService =
          services.firstWhereOrNull((service) {
        String uuid = service.uuid.toString().toLowerCase();
        return uuid == "1827" ||
            uuid == MESH_PROVISIONING_SERVICE_UUID.toLowerCase();
      });

      if (provisioningService == null) {
        print('Provisioning service not found');
        addLog('Provisioning service not found');
        await device.disconnect();
        return false;
      }

      // Find characteristics
      BluetoothCharacteristic? dataInChar =
          provisioningService.characteristics.firstWhereOrNull((char) {
        String uuid = char.uuid.toString().toLowerCase();
        return uuid == "2adb" ||
            uuid == MESH_PROVISIONING_DATA_IN_UUID.toLowerCase();
      });

      BluetoothCharacteristic? dataOutChar =
          provisioningService.characteristics.firstWhereOrNull((char) {
        String uuid = char.uuid.toString().toLowerCase();
        return uuid == "2adc" ||
            uuid == MESH_PROVISIONING_DATA_OUT_UUID.toLowerCase();
      });

      if (dataInChar == null || dataOutChar == null) {
        print('Provisioning characteristics not found');
        addLog('Provisioning characteristics not found');
        await device.disconnect();
        return false;
      }

      // Store the data in characteristic for use in the response handler
      _currentDataInChar = dataInChar;

      // Enable notifications
      await dataOutChar.setNotifyValue(true);
      print('Enabled notifications on ${dataOutChar.uuid}');
      addLog('Enabled notifications on ${dataOutChar.uuid}');

      // Create completer for provisioning
      Completer<bool> provisioningCompleter = Completer<bool>();

      // Listen for responses
      StreamSubscription<List<int>>? subscription =
          dataOutChar.value.listen((data) {
        if (data.isNotEmpty && data[0] == 0x03) {
          // Provisioning PDU
          _handleProvisioningResponse(
              data.sublist(1)); // Remove the bearer header

          // Complete the provisioning when we receive a complete or failed message
          if (data.length > 1 && (data[1] == 0x08 || data[1] == 0x09)) {
            if (!provisioningCompleter.isCompleted) {
              provisioningCompleter
                  .complete(data[1] == 0x08); // Complete with success if 0x08
            }
          }
        }
      });

      // Use provided values or defaults
      Uint8List netKeyToUse = netKey ?? _netKey;
      Uint8List appKeyToUse = appKey ?? _appKey;
      int unicastAddressToUse = unicastAddress ?? _nextUnicastAddress;

      // Start the provisioning process with Invite PDU
      await _sendProvisioningInvite(dataInChar);

      // Wait for provisioning to complete with timeout
      bool success = false;
      try {
        success = await provisioningCompleter.future.timeout(timeout);
      } catch (e) {
        print('Provisioning timeout: $e');
        addLog('Provisioning timeout: $e');
      }

      // Clean up
      _isProvisioning = false;
      await subscription?.cancel();

      if (success) {
        print('Provisioning completed successfully');
        addLog('Provisioning completed successfully');

        // Store the provisioned device
        _connectedNodes[device.id.toString()] = MeshNodeInfo(
          device: device,
          unicastAddress: unicastAddressToUse,
          netKey: netKeyToUse,
          appKey: appKeyToUse,
          dataInChar: null,
          dataOutChar: null,
        );

        // Save the device for future connections
        await saveDevice(device);

        // Update next unicast address
        _nextUnicastAddress = unicastAddressToUse + 1;

        return true;
      } else {
        print('Provisioning failed');
        addLog('Provisioning failed');
        await device.disconnect();
        return false;
      }
    } catch (e) {
      _isProvisioning = false;
      print('Error during provisioning: $e');
      addLog('Error during provisioning: $e');
      return false;
    }
  }

  // Replace the existing _startProvisioningProcess with this method
  Future<void> _sendProvisioningInvite(
      BluetoothCharacteristic dataInChar) async {
    try {
      // Step 1: Send Provisioning Invite PDU
      List<int> invitePdu = [
        0x00, // Type: Provisioning Invite
        0x05, // Attention Duration: 5 seconds
      ];

      await _sendProvisioningPdu(dataInChar, invitePdu);
      print('Sent Provisioning Invite PDU');
      addLog('Sent Provisioning Invite PDU');
    } catch (e) {
      print('Error sending provisioning invite: $e');
      addLog('Error sending provisioning invite: $e');
    }
  }

  Future<void> _sendProvisioningPdu(
      BluetoothCharacteristic dataInChar, List<int> pdu) async {
    // Maximum PDU size (including the header byte)
    const int MAX_PDU_SIZE = 20;

    // Check if PDU exceeds maximum size
    if (pdu.length >= MAX_PDU_SIZE) {
      print(
          'Warning: PDU size (${pdu.length}) exceeds maximum allowed (${MAX_PDU_SIZE - 1} after header). Truncating.');
      addLog(
          'Warning: PDU size (${pdu.length}) exceeds maximum allowed (${MAX_PDU_SIZE - 1} after header). Truncating.');

      // Truncate to fit within MTU size (leaving room for the header)
      pdu = pdu.sublist(0, MAX_PDU_SIZE - 1);
    }

    // Wrap the PDU in a Provisioning Bearer Control (PB-ADV)
    List<int> pbAdvPdu = [
      0x03, // Type: Provisioning PDU
      ...pdu,
    ];

    // Double-check the final size
    if (pbAdvPdu.length > MAX_PDU_SIZE) {
      print(
          'ERROR: Final PDU size (${pbAdvPdu.length}) still exceeds maximum (${MAX_PDU_SIZE})');
      addLog(
          'ERROR: Final PDU size (${pbAdvPdu.length}) still exceeds maximum (${MAX_PDU_SIZE})');
      pbAdvPdu = pbAdvPdu.sublist(0, MAX_PDU_SIZE);
    }

    // Send the PDU
    bool useWithoutResponse = !dataInChar.properties.write &&
        dataInChar.properties.writeWithoutResponse;

    await dataInChar.write(Uint8List.fromList(pbAdvPdu),
        withoutResponse: useWithoutResponse);
    print(
        'Sent provisioning PDU: ${pbAdvPdu.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
    addLog(
        'Sent provisioning PDU: ${pbAdvPdu.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
  }

  void _processProvisioningResponse(
      List<int> data,
      BluetoothCharacteristic dataInChar,
      Completer<bool> provisioningCompleter) async {
    try {
      if (data.isEmpty) return;

      int pduType = data[0];

      if (pduType == 0x03) {
        // Provisioning PDU
        if (data.length < 2) return;

        int messageType = data[1];
        print(
            'Received provisioning message type: 0x${messageType.toRadixString(16)}');
        addLog(
            'Received provisioning message type: 0x${messageType.toRadixString(16)}');

        switch (messageType) {
          case 0x01: // Capabilities PDU
            print('Device capabilities received');
            addLog('Device capabilities received');

            // Since OOB is disabled, skip public key exchange and go directly to provisioning data
            await _sendProvisioningDataPdu(dataInChar);
            break;

          case 0x08: // Complete PDU
            print('Provisioning complete!');
            addLog('Provisioning complete!');

            if (!provisioningCompleter.isCompleted) {
              provisioningCompleter.complete(true);
            }
            break;

          case 0x09: // Failed PDU
            String errorCode =
                data.length > 2 ? '0x${data[2].toRadixString(16)}' : 'unknown';
            print('Provisioning failed with error code: $errorCode');
            addLog('Provisioning failed with error code: $errorCode');

            if (!provisioningCompleter.isCompleted) {
              provisioningCompleter.complete(false);
            }
            break;
        }
      }
    } catch (e) {
      print('Error processing provisioning response: $e');
      addLog('Error processing provisioning response: $e');

      if (!provisioningCompleter.isCompleted) {
        provisioningCompleter.complete(false);
      }
    }
  }

  Future<void> _sendPublicKeyPdu(BluetoothCharacteristic dataInChar) async {
    try {
      // First chunk - header + first part of key (max 19 bytes total)
      List<int> publicKeyPdu1 = [
        0x02, // Type: Public Key
        // First 18 bytes of the public key (to stay within 20 byte limit with header)
        ...List.filled(18, 0x01),
      ];

      await _sendProvisioningPdu(dataInChar, publicKeyPdu1);
      await Future.delayed(Duration(milliseconds: 100));

      // Remaining chunks - send in 20-byte segments
      for (int i = 0; i < 3; i++) {
        List<int> chunk = List.filled(20, 0x02 + i);
        await _sendProvisioningPduContinuation(dataInChar, chunk);
        await Future.delayed(Duration(milliseconds: 100));
      }

      print('Sent Public Key PDU (in chunks)');
      addLog('Sent Public Key PDU (in chunks)');
    } catch (e) {
      print('Error sending public key: $e');
      addLog('Error sending public key: $e');
    }
  }

  // Add this method to send continuation packets
  Future<void> _sendProvisioningPduContinuation(
      BluetoothCharacteristic dataInChar, List<int> data) async {
    // Send continuation data without the provisioning header
    const int MAX_DATA_SIZE = 20;

    if (data.length > MAX_DATA_SIZE) {
      print(
          'Warning: Continuation data size (${data.length}) exceeds maximum (${MAX_DATA_SIZE}). Truncating.');
      addLog(
          'Warning: Continuation data size (${data.length}) exceeds maximum (${MAX_DATA_SIZE}). Truncating.');
      data = data.sublist(0, MAX_DATA_SIZE);
    }

    bool useWithoutResponse = !dataInChar.properties.write &&
        dataInChar.properties.writeWithoutResponse;

    await dataInChar.write(Uint8List.fromList(data),
        withoutResponse: useWithoutResponse);
    print(
        'Sent continuation data: ${data.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
    addLog(
        'Sent continuation data: ${data.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
  }

  Future<void> _sendConfirmationPdu(BluetoothCharacteristic dataInChar) async {
    try {
      // In a real implementation, this would be a CMAC of the provisioning data
      // For simplicity, we'll use a fixed value
      List<int> confirmationPdu = [
        0x04, // Type: Provisioning Confirmation
        // Only send 18 bytes to stay within 20-byte limit (including header)
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
        0x11, 0x12,
      ];

      await _sendProvisioningPdu(dataInChar, confirmationPdu);
      print('Sent Confirmation PDU');
      addLog('Sent Confirmation PDU');

      // If we need to send more bytes, send them as continuation
      List<int> confirmationPduCont = [
        0x13,
        0x14,
        0x15,
        0x16,
        0x17,
        0x18,
        0x19,
        0x20,
      ];

      await _sendProvisioningPduContinuation(dataInChar, confirmationPduCont);
      print('Sent Confirmation PDU continuation');
      addLog('Sent Confirmation PDU continuation');
    } catch (e) {
      print('Error sending confirmation: $e');
      addLog('Error sending confirmation: $e');
    }
  }

  Future<void> _sendRandomPdu(BluetoothCharacteristic dataInChar) async {
    try {
      // In real implementation, this would be the random value used in confirmation
      // For simplicity, we'll use a fixed value
      List<int> randomPdu = [
        0x06, // Type: Provisioning Random
        // Only send 18 bytes to stay within 20-byte limit (including header)
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
        0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00,
        0x01, 0x02,
      ];

      await _sendProvisioningPdu(dataInChar, randomPdu);
      print('Sent Random PDU');
      addLog('Sent Random PDU');

      // If we need to send more bytes, send them as continuation
      List<int> randomPduCont = [
        0x03,
        0x04,
        0x05,
        0x06,
        0x07,
        0x08,
      ];

      await _sendProvisioningPduContinuation(dataInChar, randomPduCont);
      print('Sent Random PDU continuation');
      addLog('Sent Random PDU continuation');
    } catch (e) {
      print('Error sending random: $e');
      addLog('Error sending random: $e');
    }
  }

  Future<void> _sendProvisioningDataPdu(
      BluetoothCharacteristic dataInChar) async {
    try {
      // ESP-IDF expects complete provisioning data (25 bytes total)
      // Network Key (16) + Key Index (2) + Flags (1) + IV Index (4) + Unicast Address (2)
      List<int> provisioningData = [
        // Network Key (16 bytes)
        0x00, 0x20, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12,
        0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12,
        // Key Index (2 bytes) - NetKey Index
        0x00, 0x00,
        // Flags (1 byte) - Key Refresh = 0, IV Update = 0
        0x00,
        // IV Index (4 bytes)
        0x00, 0x00, 0x00, 0x00,
        // Unicast Address (2 bytes)
        0x00, 0x02,
      ];

      // Send as single PDU with header
      List<int> dataPdu = [0x07] + provisioningData; // Type + Data

      await _sendProvisioningPdu(dataInChar, dataPdu);
      print('Sent Provisioning Data PDU (${dataPdu.length} bytes)');
      addLog('Sent Provisioning Data PDU (${dataPdu.length} bytes)');
    } catch (e) {
      print('Error sending provisioning data: $e');
      addLog('Error sending provisioning data: $e');
    }
  }

  // Add this method to request a larger MTU size
  Future<void> _requestMtu(BluetoothDevice device, int mtu) async {
    try {
      await device.requestMtu(mtu);
      print('Requested MTU size: $mtu');
      addLog('Requested MTU size: $mtu');
    } catch (e) {
      print('Failed to request MTU: $e');
      addLog('Failed to request MTU: $e');
    }
  }
}

class MeshNodeInfo {
  final BluetoothDevice device;
  final int unicastAddress;
  final Uint8List netKey;
  final Uint8List appKey;
  final BluetoothCharacteristic? dataInChar;
  final BluetoothCharacteristic? dataOutChar;

  MeshNodeInfo({
    required this.device,
    required this.unicastAddress,
    required this.netKey,
    required this.appKey,
    this.dataInChar,
    this.dataOutChar,
  });
}

class DeviceStatus {
  final bool isOnline;
  final DateTime lastSeen;
  final Map<String, dynamic> sensorData;
  final String? lastCommand;
  final bool? lastCommandSuccess;

  DeviceStatus({
    required this.isOnline,
    required this.lastSeen,
    this.sensorData = const {},
    this.lastCommand,
    this.lastCommandSuccess,
  });
}

class SavedDeviceInfo {
  final String id;
  final String name;
  final DateTime lastSeen;

  SavedDeviceInfo({
    required this.id,
    required this.name,
    required this.lastSeen,
  });
}

// Extension methods for better device identification
extension BluetoothDeviceExtension on BluetoothDevice {
  bool get isMeshDevice {
    // Check if device name contains mesh-related keywords
    String deviceName = name.toLowerCase();
    return deviceName.contains('mesh') ||
        deviceName.contains('esp') ||
        deviceName.contains('ble_mesh') ||
        deviceName.contains('esp32') ||
        deviceName.contains('node') ||
        deviceName
            .isEmpty; // Many provisioned mesh devices don't advertise names
  }

  String get displayName {
    return name.isEmpty ? 'ESP32 Mesh Node' : name;
  }
}

class ProvisioningPage extends StatefulWidget {
  final BLEMeshController meshController;

  ProvisioningPage({required this.meshController});

  @override
  _ProvisioningPageState createState() => _ProvisioningPageState();
}

class _ProvisioningPageState extends State<ProvisioningPage> {
  List<BluetoothDevice> _unprovisionedDevices = [];
  bool _isScanning = false;
  String _statusText = 'Ready to scan for unprovisioned devices';
  BluetoothDevice? _selectedDevice;
  int _provisioningStep = 0;
  bool _provisioningInProgress = false;

  // Provisioning data
  final TextEditingController _netKeyController =
      TextEditingController(text: '00201212121212121212121212121212');
  final TextEditingController _appKeyController =
      TextEditingController(text: '12121212121212121212121212121212');
  final TextEditingController _unicastAddressController =
      TextEditingController(text: '0002');

  @override
  void initState() {
    super.initState();
  }

  Future<void> _startScanningForUnprovisionedDevices() async {
    if (_isScanning) return;

    setState(() {
      _isScanning = true;
      _statusText = 'Scanning for unprovisioned BLE Mesh devices...';
      _unprovisionedDevices.clear();
      _selectedDevice = null;
      _provisioningStep = 0;
    });

    try {
      // Start scanning for unprovisioned BLE Mesh devices with Provisioning Service
      await FlutterBluePlus.startScan(
        timeout: Duration(seconds: 10),
        withServices: [
          Guid(
              "00001827-0000-1000-8000-00805f9b34fb"), // Mesh Provisioning Service
        ],
      );

      // Listen to scan results
      FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          // Add all devices found with Mesh Provisioning Service
          if (!_unprovisionedDevices
              .any((device) => device.id == result.device.id)) {
            setState(() {
              _unprovisionedDevices.add(result.device);
            });
          }
        }
      });

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((scanning) => !scanning).first;

      setState(() {
        _isScanning = false;
        _statusText =
            'Scan complete. Found ${_unprovisionedDevices.length} unprovisioned devices';
      });
    } catch (e) {
      setState(() {
        _isScanning = false;
        _statusText = 'Error scanning: $e';
      });
    }
  }

  Future<void> _selectDeviceForProvisioning(BluetoothDevice device) async {
    setState(() {
      _selectedDevice = device;
      _provisioningStep = 1;
      _statusText =
          'Selected device: ${device.name.isEmpty ? 'Unknown Device' : device.name}';
    });
  }

  Future<void> _startProvisioning() async {
    if (_selectedDevice == null) return;

    setState(() {
      _provisioningInProgress = true;
      _provisioningStep = 2;
      _statusText = 'Provisioning in progress...';
    });

    try {
      bool success = await widget.meshController.provisionDevice(
        _selectedDevice!,
        netKey: _hexStringToBytes(_netKeyController.text),
        appKey: _hexStringToBytes(_appKeyController.text),
        unicastAddress: int.parse(_unicastAddressController.text, radix: 16),
      );

      setState(() {
        _provisioningInProgress = false;
        _provisioningStep = success ? 3 : 0;
        _statusText = success
            ? 'Device provisioned successfully'
            : 'Failed to provision device';
      });
    } catch (e) {
      setState(() {
        _provisioningInProgress = false;
        _provisioningStep = 0;
        _statusText = 'Error provisioning device: $e';
      });
    }
  }

  Uint8List _hexStringToBytes(String hex) {
    // Remove any spaces or non-hex characters
    hex = hex.replaceAll(RegExp(r'[^0-9A-Fa-f]'), '');

    // Ensure even length
    if (hex.length % 2 != 0) {
      hex = '0' + hex;
    }

    List<int> bytes = [];
    for (int i = 0; i < hex.length; i += 2) {
      String hexByte = hex.substring(i, i + 2);
      bytes.add(int.parse(hexByte, radix: 16));
    }

    return Uint8List.fromList(bytes);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('BLE Mesh Provisioning'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_statusText),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _isScanning
                      ? null
                      : _startScanningForUnprovisionedDevices,
                  child: Text(_isScanning
                      ? 'Scanning...'
                      : 'Scan for Unprovisioned Devices'),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _unprovisionedDevices.clear();
                      _statusText = 'Device list cleared';
                      _selectedDevice = null;
                      _provisioningStep = 0;
                    });
                  },
                  child: Text('Clear List'),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Unprovisioned Devices:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 1,
              child: ListView.builder(
                itemCount: _unprovisionedDevices.length,
                itemBuilder: (context, index) {
                  final device = _unprovisionedDevices[index];
                  return Card(
                    child: ListTile(
                      title: Text(
                          device.name.isEmpty ? 'Unknown Device' : device.name),
                      subtitle: Text(device.id.toString()),
                      trailing: ElevatedButton(
                        onPressed: () => _selectDeviceForProvisioning(device),
                        child: Text('Select'),
                      ),
                    ),
                  );
                },
              ),
            ),
            if (_selectedDevice != null) ...[
              SizedBox(height: 16),
              Text(
                'Provisioning Configuration:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                          'Selected Device: ${_selectedDevice!.name.isEmpty ? 'Unknown Device' : _selectedDevice!.name}'),
                      Text('Device ID: ${_selectedDevice!.id}'),
                      SizedBox(height: 16),
                      TextField(
                        controller: _netKeyController,
                        decoration: InputDecoration(
                          labelText: 'Network Key (hex)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 8),
                      TextField(
                        controller: _appKeyController,
                        decoration: InputDecoration(
                          labelText: 'Application Key (hex)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 8),
                      TextField(
                        controller: _unicastAddressController,
                        decoration: InputDecoration(
                          labelText: 'Unicast Address (hex)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 16),
                      Center(
                        child: ElevatedButton(
                          onPressed: _provisioningInProgress
                              ? null
                              : _startProvisioning,
                          child: Text(_provisioningInProgress
                              ? 'Provisioning...'
                              : 'Start Provisioning'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: EdgeInsets.symmetric(
                                horizontal: 32, vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            if (_provisioningStep == 3) ...[
              SizedBox(height: 16),
              Card(
                color: Colors.green[100],
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Device Provisioned Successfully!',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      SizedBox(height: 8),
                      Text('The device has been added to your mesh network.'),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text('Return to Main Screen'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
